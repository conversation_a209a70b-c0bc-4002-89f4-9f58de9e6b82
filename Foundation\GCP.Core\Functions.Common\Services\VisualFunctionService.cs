using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.VisualFunction;
using System.Diagnostics;

namespace GCP.Functions.Common.Services
{
    /// <summary>
    /// 可视化函数服务
    /// </summary>
    [Function("visualFunction", "可视化函数服务")]
    internal class VisualFunctionService : BaseService
    {
        /// <summary>
        /// 执行可视化函数
        /// </summary>
        /// <param name="request">执行请求</param>
        /// <returns>执行结果</returns>
        [Function("execute", "执行可视化函数")]
        public async Task<VisualFunctionExecuteResponse> ExecuteAsync(VisualFunctionExecuteRequest request)
        {
            try
            {


                var stopwatch = Stopwatch.StartNew();
                var engine = new VisualFunctionEngine(this.Context.db);
                var response = new VisualFunctionExecuteResponse();

                // 生成C#表达式
                response.GeneratedExpression = engine.GenerateCSharpExpression(request.Steps);

                // 如果只需要生成表达式，直接返回
                if (request.GenerateExpressionOnly)
                {
                    response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
                    return response;
                }

                // 执行函数
                if (request.UseCompiledExpression)
                {
                    // 使用编译表达式执行（高性能模式）
                    response.Result = await engine.ExecuteAsync(request.Steps, request.InputData, true);

                    // 生成简化的步骤结果详情
                    response.StepResults = request.Steps.OrderBy(s => s.Order).Select(step => new VisualFunctionStepResult
                    {
                        StepId = step.Id,
                        StepName = step.DisplayName,
                        Result = $"步骤 {step.Order + 1} 执行完成",
                        ExecutionTimeMs = response.ExecutionTimeMs / request.Steps.Count, // 平均分配时间
                        Success = true
                    }).ToList();
                }
                else
                {
                    // 使用解释执行（调试模式，获取详细步骤信息）
                    var context = new VisualFunctionContext
                    {
                        InputData = request.InputData ?? new Dictionary<string, object>(),
                        Variables = new Dictionary<string, object>(),
                        StepResults = new Dictionary<string, object>()
                    };

                    response.Result = await engine.ExecuteInterpretedWithDetailsAsync(request.Steps, context);
                    response.StepResults = context.StepExecutionDetails;
                }

                response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;

                return response;
            }
            catch (Exception ex)
            {
                return new VisualFunctionExecuteResponse
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ExecutionTimeMs = 0,
                    StepResults = request.Steps.Select(step => new VisualFunctionStepResult
                    {
                        StepId = step.Id,
                        StepName = step.DisplayName,
                        Success = false,
                        ErrorMessage = ex.Message
                    }).ToList()
                };
            }
        }

        /// <summary>
        /// 测试单个函数步骤
        /// </summary>
        /// <param name="step">函数步骤</param>
        /// <param name="inputData">输入数据</param>
        /// <returns>测试结果</returns>
        [Function("testStep", "测试单个函数步骤")]
        public async Task<object> TestStepAsync(VisualFunctionStep step, Dictionary<string, object>? inputData = null)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                var engine = new VisualFunctionEngine(this.Context.db);
                
                var steps = new List<VisualFunctionStep> { step };
                var result = await engine.ExecuteAsync(steps, inputData ?? new Dictionary<string, object>());
                
                return new
                {
                    Success = true,
                    Result = result,
                    ExecutionTimeMs = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取可用的内置函数列表
        /// </summary>
        /// <returns>函数列表</returns>
        [Function("getBuiltinFunctions", "获取内置函数列表")]
        public List<object> GetBuiltinFunctions()
        {
            try
            {
                var functions = GetBuiltinFunctionList();
                return functions;
            }
            catch (Exception ex)
            {
                throw new CustomException($"获取内置函数列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证可视化函数配置
        /// </summary>
        /// <param name="steps">函数步骤列表</param>
        /// <returns>验证结果</returns>
        [Function("validate", "验证可视化函数配置")]
        public object ValidateConfiguration(List<VisualFunctionStep> steps)
        {
            try
            {
                var validationResult = ValidateSteps(steps);
                return validationResult;
            }
            catch (Exception ex)
            {
                throw new CustomException($"验证配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存可视化函数配置（已移除，不再需要单独的配置表）
        /// </summary>
        /// <param name="configuration">函数配置</param>
        /// <returns>保存结果</returns>
        [Function("saveConfiguration", "保存可视化函数配置")]
        public async Task<object> SaveConfigurationAsync(VisualFunctionConfiguration configuration)
        {
            // 可视化函数配置不再需要单独保存，直接在使用时执行
            return new { Success = true, Message = "可视化函数配置无需保存，直接执行即可" };
        }

        /// <summary>
        /// 获取保存的可视化函数配置列表（已移除）
        /// </summary>
        /// <returns>配置列表</returns>
        [Function("getConfigurations", "获取可视化函数配置列表")]
        public async Task<List<VisualFunctionConfiguration>> GetConfigurationsAsync()
        {
            // 不再需要保存配置，返回空列表
            return new List<VisualFunctionConfiguration>();
        }

        /// <summary>
        /// 删除可视化函数配置（已移除）
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>删除结果</returns>
        [Function("deleteConfiguration", "删除可视化函数配置")]
        public async Task<object> DeleteConfigurationAsync(string id)
        {
            // 不再需要删除配置
            return new { Success = true, Message = "无需删除配置" };
        }

        #region 私有方法

        /// <summary>
        /// 获取内置函数列表
        /// </summary>
        private List<object> GetBuiltinFunctionList()
        {
            var functions = new List<object>();
            var utilsType = typeof(ScriptExtensions.JavascriptUtils);
            var methods = utilsType.GetMethods(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance)
                .Where(m => !m.IsSpecialName && m.DeclaringType == utilsType);

            foreach (var method in methods)
            {
                var parameters = method.GetParameters().Select(p => new
                {
                    Name = p.Name,
                    Type = GetParameterTypeName(p.ParameterType),
                    Required = !p.HasDefaultValue,
                    DefaultValue = p.HasDefaultValue ? p.DefaultValue : null,
                    Description = GetParameterDescription(p.Name)
                }).ToList();

                functions.Add(new
                {
                    Value = method.Name,
                    Name = method.Name,
                    Label = GetFunctionDisplayName(method.Name),
                    DisplayName = GetFunctionDisplayName(method.Name),
                    Description = GetFunctionDescription(method.Name),
                    Remark = GetFunctionDescription(method.Name),
                    Script = GenerateFunctionScript(method.Name, method.GetParameters()),
                    Category = GetFunctionCategory(method.Name),
                    Parameters = parameters,
                    ReturnType = GetParameterTypeName(method.ReturnType),
                    OutputType = GetOutputType(method.Name)
                });
            }

            return functions.OrderBy(f => ((dynamic)f).Category).ThenBy(f => ((dynamic)f).Label).ToList();
        }

        /// <summary>
        /// 获取函数显示名称
        /// </summary>
        private string GetFunctionDisplayName(string functionName)
        {
            return functionName switch
            {
                // Common Functions
                "UUID" => "Generate UUID",
                "NOW" => "Current Time",
                "IF" => "Conditional",
                "DATE_TO_STRING" => "Date to String",
                "JSON_PARSE" => "JSON Parse",
                "DB_FIRST" => "Database Query",

                // String Functions
                "STRING_CONCAT" => "String Concatenation",
                "STRING_SUBSTRING" => "String Substring",
                "STRING_REPLACE" => "String Replace",
                "STRING_TRIM" => "String Trim",
                "STRING_UPPER" => "String to Upper",
                "STRING_LOWER" => "String to Lower",
                "STRING_SPLIT" => "String Split",
                "STRING_LENGTH" => "String Length",
                "STRING_CONTAINS" => "String Contains",
                "STRING_STARTS_WITH" => "String Starts With",
                "STRING_ENDS_WITH" => "String Ends With",

                // Array Functions
                "ARRAY_LENGTH" => "Array Length",
                "ARRAY_CONTAINS" => "Array Contains",
                "ARRAY_FIRST" => "Array First Element",
                "ARRAY_LAST" => "Array Last Element",
                "ARRAY_JOIN" => "Array Join",
                "GROUP_BY_DYNAMIC_FIELD" => "Dynamic Group By",

                // Date Functions
                "DATE_ADD" => "Date Add/Subtract",
                "DATE_DIFF" => "Date Difference",
                "DATE_FORMAT" => "Date Format",
                "DATE_PARSE" => "Date Parse",
                "DATE_TIMESTAMP" => "Date to Timestamp",
                "DATE_FROM_TIMESTAMP" => "Timestamp to Date",

                // Dictionary Functions
                "DICT_GET" => "Dictionary Get",
                "DICT_SET" => "Dictionary Set",
                "DICT_MERGE" => "Dictionary Merge",
                "DICT_KEYS" => "Dictionary Keys",
                "DICT_VALUES" => "Dictionary Values",
                "DICT_HAS_KEY" => "Dictionary Has Key",

                // Math Functions
                "COMPARE" => "Number Compare",
                "MATH_CALC" => "Math Calculation",
                "MATH_ROUND" => "Math Round",
                "MATH_CEIL" => "Math Ceiling",
                "MATH_FLOOR" => "Math Floor",
                "MATH_ABS" => "Math Absolute",

                _ => functionName
            };
        }

        /// <summary>
        /// 获取函数描述
        /// </summary>
        private string GetFunctionDescription(string functionName)
        {
            return functionName switch
            {
                // Common Functions
                "UUID" => "Generate a random UUID string",
                "NOW" => "Get current date and time",
                "IF" => "Return value based on condition",
                "DATE_TO_STRING" => "Convert date to formatted string",
                "JSON_PARSE" => "Parse JSON string to object",
                "DB_FIRST" => "Execute SQL query and return first result",

                // String Functions
                "STRING_CONCAT" => "Concatenate multiple values into a string",
                "STRING_SUBSTRING" => "Extract substring from specified position",
                "STRING_REPLACE" => "Replace specified content in string",
                "STRING_TRIM" => "Remove leading and trailing whitespace",
                "STRING_UPPER" => "Convert string to uppercase",
                "STRING_LOWER" => "Convert string to lowercase",
                "STRING_SPLIT" => "Split string by separator",
                "STRING_LENGTH" => "Get string length",
                "STRING_CONTAINS" => "Check if string contains specified value",
                "STRING_STARTS_WITH" => "Check if string starts with specified value",
                "STRING_ENDS_WITH" => "Check if string ends with specified value",

                // Array Functions
                "ARRAY_LENGTH" => "Get array or collection length",
                "ARRAY_CONTAINS" => "Check if array contains specified element",
                "ARRAY_FIRST" => "Get first element of array",
                "ARRAY_LAST" => "Get last element of array",
                "ARRAY_JOIN" => "Join array elements into string",
                "GROUP_BY_DYNAMIC_FIELD" => "Group array by dynamic fields",

                // Date Functions
                "DATE_ADD" => "Add or subtract time from date",
                "DATE_DIFF" => "Calculate difference between two dates",
                "DATE_FORMAT" => "Format date to string",
                "DATE_PARSE" => "Parse string to date",
                "DATE_TIMESTAMP" => "Convert date to timestamp",
                "DATE_FROM_TIMESTAMP" => "Convert timestamp to date",

                // Dictionary Functions
                "DICT_GET" => "Get value from dictionary by key",
                "DICT_SET" => "Set key-value pair in dictionary",
                "DICT_MERGE" => "Merge multiple dictionaries",
                "DICT_KEYS" => "Get all keys from dictionary",
                "DICT_VALUES" => "Get all values from dictionary",
                "DICT_HAS_KEY" => "Check if dictionary contains key",

                // Math Functions
                "COMPARE" => "Compare two numeric values",
                "MATH_CALC" => "Perform basic mathematical operations",
                "MATH_ROUND" => "Round number to specified decimal places",
                "MATH_CEIL" => "Round number up to nearest integer",
                "MATH_FLOOR" => "Round number down to nearest integer",
                "MATH_ABS" => "Get absolute value of number",

                _ => $"Execute {functionName} function"
            };
        }

        /// <summary>
        /// 获取函数分类
        /// </summary>
        private string GetFunctionCategory(string functionName)
        {
            return functionName switch
            {
                "UUID" or "NOW" or "IF" or "DATE_TO_STRING" or "JSON_PARSE" or "DB_FIRST" => "Common",
                "STRING_CONCAT" or "STRING_SUBSTRING" or "STRING_REPLACE" or "STRING_TRIM" or
                "STRING_UPPER" or "STRING_LOWER" or "STRING_SPLIT" or "STRING_LENGTH" or
                "STRING_CONTAINS" or "STRING_STARTS_WITH" or "STRING_ENDS_WITH" => "String",
                "ARRAY_LENGTH" or "ARRAY_CONTAINS" or "ARRAY_FIRST" or "ARRAY_LAST" or
                "ARRAY_JOIN" or "GROUP_BY_DYNAMIC_FIELD" => "Array",
                "DATE_ADD" or "DATE_DIFF" or "DATE_FORMAT" or "DATE_PARSE" or
                "DATE_TIMESTAMP" or "DATE_FROM_TIMESTAMP" => "DateTime",
                "DICT_GET" or "DICT_SET" or "DICT_MERGE" or "DICT_KEYS" or
                "DICT_VALUES" or "DICT_HAS_KEY" => "Dictionary",
                "COMPARE" or "MATH_CALC" or "MATH_ROUND" or "MATH_CEIL" or
                "MATH_FLOOR" or "MATH_ABS" => "Math",
                _ => "Other"
            };
        }

        /// <summary>
        /// 获取参数类型名称
        /// </summary>
        private string GetParameterTypeName(Type type)
        {
            if (type == typeof(string)) return "string";
            if (type == typeof(int) || type == typeof(int?)) return "number";
            if (type == typeof(double) || type == typeof(double?)) return "number";
            if (type == typeof(bool) || type == typeof(bool?)) return "boolean";
            if (type == typeof(DateTime) || type == typeof(DateTime?)) return "DateTime";
            if (type.IsArray || (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>))) return "array";
            if (type == typeof(object) || type.Name.Contains("Dictionary")) return "object";
            return "any";
        }

        /// <summary>
        /// 获取参数描述
        /// </summary>
        private string GetParameterDescription(string paramName)
        {
            return paramName switch
            {
                "input" => "Input string",
                "array" => "Input array",
                "dict" => "Dictionary object",
                "date" => "Date object",
                "date1" => "First date",
                "date2" => "Second date",
                "value" => "Input value",
                "value1" => "First value",
                "value2" => "Second value",
                "condition" => "Boolean condition",
                "trueValue" => "Value when condition is true",
                "falseValue" => "Value when condition is false",
                "key" => "Dictionary key",
                "time" => "Time object",
                "jsonString" => "JSON string",
                "dateString" => "Date string",
                "timestamp" => "Unix timestamp",
                "separator" => "Separator character",
                "format" => "Format string",
                "startIndex" => "Start position",
                "length" => "Length",
                "oldValue" => "Value to replace",
                "newValue" => "New value",
                "defaultValue" => "Default value",
                "digits" => "Decimal places",
                "unit" => "Time unit",
                "groupFields" => "Group fields array",
                "fields" => "Other fields array",
                "childField" => "Child field name",
                "dict1" => "First dictionary",
                "dict2" => "Second dictionary",
                "operator_" => "Math operator",
                "sqlString" => "SQL query string",
                "args" => "Query parameters",
                _ => $"{paramName} parameter"
            };
        }

        /// <summary>
        /// 生成函数脚本
        /// </summary>
        private string GenerateFunctionScript(string functionName, System.Reflection.ParameterInfo[] parameters)
        {
            var paramList = parameters.Select(p =>
            {
                if (p.HasDefaultValue)
                {
                    var defaultValue = p.DefaultValue?.ToString() ?? "null";
                    if (p.ParameterType == typeof(string) && p.DefaultValue != null)
                        defaultValue = $"\"{defaultValue}\"";
                    return p.Name;
                }
                return p.Name;
            });

            return $"Utils.{functionName}({string.Join(", ", paramList)})";
        }

        /// <summary>
        /// 获取输出类型
        /// </summary>
        private string GetOutputType(string functionName)
        {
            return functionName switch
            {
                "UUID" or "STRING_CONCAT" or "STRING_SUBSTRING" or "STRING_REPLACE" or
                "STRING_TRIM" or "STRING_UPPER" or "STRING_LOWER" or "DATE_TO_STRING" or
                "DATE_FORMAT" or "ARRAY_JOIN" => "string",

                "ARRAY_LENGTH" or "STRING_LENGTH" or "DATE_DIFF" or "DATE_TIMESTAMP" or
                "MATH_CALC" or "MATH_ROUND" or "MATH_CEIL" or "MATH_FLOOR" or "MATH_ABS" => "number",

                "IF" or "COMPARE" or "ARRAY_CONTAINS" or "STRING_CONTAINS" or
                "STRING_STARTS_WITH" or "STRING_ENDS_WITH" or "DICT_HAS_KEY" => "boolean",

                "NOW" or "DATE_ADD" or "DATE_PARSE" or "DATE_FROM_TIMESTAMP" => "DateTime",

                "STRING_SPLIT" or "DICT_KEYS" or "DICT_VALUES" or "GROUP_BY_DYNAMIC_FIELD" => "array",

                _ => "object"
            };
        }

        /// <summary>
        /// 验证函数步骤
        /// </summary>
        private object ValidateSteps(List<VisualFunctionStep> steps)
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            if (steps == null || steps.Count == 0)
            {
                errors.Add("至少需要一个函数步骤");
                return new { Success = false, Errors = errors, Warnings = warnings };
            }

            foreach (var step in steps)
            {
                // 验证函数名称
                if (string.IsNullOrEmpty(step.FunctionName))
                {
                    errors.Add($"步骤 {step.Order + 1} 缺少函数名称");
                }

                // 验证参数
                foreach (var param in step.Parameters)
                {
                    if (param.Required && (param.Value == null || string.IsNullOrEmpty(param.Value.ToString())))
                    {
                        errors.Add($"步骤 {step.Order + 1} 的必需参数 '{param.Name}' 不能为空");
                    }
                }

                // 检查输出变量名冲突
                if (!string.IsNullOrEmpty(step.OutputVariable))
                {
                    var duplicates = steps.Where(s => s.Id != step.Id && s.OutputVariable == step.OutputVariable).ToList();
                    if (duplicates.Any())
                    {
                        warnings.Add($"输出变量名 '{step.OutputVariable}' 在多个步骤中重复使用");
                    }
                }
            }

            return new
            {
                Success = errors.Count == 0,
                Errors = errors,
                Warnings = warnings
            };
        }

        #endregion
    }
}
