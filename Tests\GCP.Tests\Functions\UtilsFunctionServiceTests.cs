using GCP.Functions.Common.Services;
using Microsoft.Extensions.DependencyInjection;
using Xunit;
using Xunit.Abstractions;

namespace GCP.Tests.Functions
{
    /// <summary>
    /// Utils工具函数服务测试
    /// </summary>
    public class UtilsFunctionServiceTests : BaseTest
    {
        public UtilsFunctionServiceTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public void GetAll_ShouldReturnFunctionList()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetAll();

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);
            
            // 验证返回的函数包含基本字段
            var firstFunction = result.First() as dynamic;
            Assert.NotNull(firstFunction);
            Assert.NotNull(firstFunction.value);
            Assert.NotNull(firstFunction.label);
            Assert.NotNull(firstFunction.script);
            Assert.NotNull(firstFunction.remark);
            Assert.NotNull(firstFunction.category);
            
            Output.WriteLine($"获取到 {result.Count} 个工具函数");
            foreach (dynamic func in result.Take(5))
            {
                Output.WriteLine($"函数: {func.value} - {func.label} ({func.category})");
            }
        }

        [Fact]
        public void GetByCategory_ShouldReturnCategorizedFunctions()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetByCategory();

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);
            
            // 验证分类结构
            var firstCategory = result.First() as dynamic;
            Assert.NotNull(firstCategory);
            Assert.NotNull(firstCategory.value);
            Assert.NotNull(firstCategory.label);
            Assert.NotNull(firstCategory.children);
            
            var children = firstCategory.children as List<object>;
            Assert.NotNull(children);
            Assert.NotEmpty(children);
            
            Output.WriteLine($"获取到 {result.Count} 个函数分类");
            foreach (dynamic category in result)
            {
                var categoryChildren = category.children as List<object>;
                Output.WriteLine($"分类: {category.label} - {categoryChildren?.Count ?? 0} 个函数");
            }
        }

        [Fact]
        public void GetAll_ShouldIncludeCommonFunctions()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetAll();

            // Assert
            var functionValues = result.Select(f => ((dynamic)f).value as string).ToList();
            
            // 验证包含常用函数
            Assert.Contains("UUID", functionValues);
            Assert.Contains("NOW", functionValues);
            Assert.Contains("STRING_CONCAT", functionValues);
            Assert.Contains("ARRAY_LENGTH", functionValues);
            Assert.Contains("DATE_ADD", functionValues);
            Assert.Contains("DICT_GET", functionValues);
            Assert.Contains("MATH_CALC", functionValues);
            
            Output.WriteLine("验证常用函数存在: 通过");
        }

        [Fact]
        public void GetByCategory_ShouldHaveExpectedCategories()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetByCategory();

            // Assert
            var categoryValues = result.Select(c => ((dynamic)c).value as string).ToList();
            
            // 验证包含预期的分类
            Assert.Contains("Common", categoryValues);
            Assert.Contains("String", categoryValues);
            Assert.Contains("Array", categoryValues);
            Assert.Contains("DateTime", categoryValues);
            Assert.Contains("Dictionary", categoryValues);
            Assert.Contains("Math", categoryValues);
            
            Output.WriteLine("验证函数分类存在: 通过");
        }

        [Fact]
        public void Functions_ShouldHaveEnglishLabels()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetAll();

            // Assert
            foreach (dynamic func in result)
            {
                string label = func.label;
                string value = func.value;
                
                // 验证标签是英文的（不包含中文字符）
                bool hasChineseChars = label.Any(c => c >= 0x4e00 && c <= 0x9fff);
                Assert.False(hasChineseChars, $"函数 {value} 的标签 '{label}' 包含中文字符");
            }
            
            Output.WriteLine("验证函数标签为英文: 通过");
        }
    }
}
