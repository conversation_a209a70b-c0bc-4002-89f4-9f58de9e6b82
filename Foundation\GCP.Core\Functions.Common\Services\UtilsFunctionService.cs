using GCP.Common;
using GCP.Functions.Common.ScriptExtensions;
using System.Reflection;

namespace GCP.Functions.Common.Services
{
    /// <summary>
    /// Utils工具函数服务
    /// </summary>
    [Function("utilsFunction", "Utils工具函数服务")]
    internal class UtilsFunctionService : BaseService
    {
        /// <summary>
        /// 获取所有Utils工具函数列表
        /// </summary>
        /// <returns>函数列表</returns>
        [Function("getAll", "获取所有Utils工具函数")]
        public List<object> GetAll()
        {
            try
            {
                var functions = new List<object>();
                var utilsType = typeof(JavascriptUtils);
                var methods = utilsType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                    .Where(m => !m.IsSpecialName && m.DeclaringType == utilsType);

                foreach (var method in methods)
                {
                    var parameters = method.GetParameters().Select(p => new
                    {
                        name = p.Name,
                        type = GetParameterTypeName(p.ParameterType),
                        required = !p.HasDefaultValue,
                        defaultValue = p.HasDefaultValue ? p.DefaultValue : null,
                        description = GetParameterDescription(p.Name)
                    }).ToList();

                    functions.Add(new
                    {
                        value = method.Name,
                        label = GetFunctionDisplayName(method.Name),
                        script = GenerateFunctionScript(method.Name, method.GetParameters()),
                        remark = GetFunctionDescription(method.Name),
                        category = GetFunctionCategory(method.Name),
                        parameters = parameters,
                        returnType = GetParameterTypeName(method.ReturnType),
                        outputType = GetOutputType(method.Name)
                    });
                }

                return functions.OrderBy(f => ((dynamic)f).category).ThenBy(f => ((dynamic)f).label).ToList();
            }
            catch (Exception ex)
            {
                throw new CustomException($"获取Utils工具函数列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 按分类获取Utils工具函数
        /// </summary>
        /// <returns>按分类分组的函数列表</returns>
        [Function("getByCategory", "按分类获取Utils工具函数")]
        public List<object> GetByCategory()
        {
            try
            {
                var allFunctions = GetAll();
                var categories = new Dictionary<string, string>
                {
                    { "Common", "Common Functions" },
                    { "String", "String Processing" },
                    { "Array", "Array Operations" },
                    { "DateTime", "Date & Time" },
                    { "Dictionary", "Dictionary Operations" },
                    { "Math", "Mathematical Operations" }
                };

                var result = new List<object>();
                foreach (var category in categories)
                {
                    var categoryFunctions = allFunctions
                        .Where(f => ((dynamic)f).category == category.Key)
                        .ToList();

                    if (categoryFunctions.Any())
                    {
                        result.Add(new
                        {
                            value = category.Key,
                            label = category.Value,
                            children = categoryFunctions
                        });
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new CustomException($"按分类获取Utils工具函数失败: {ex.Message}", ex);
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取函数显示名称
        /// </summary>
        private string GetFunctionDisplayName(string functionName)
        {
            return functionName switch
            {
                // Common Functions
                "UUID" => "Generate UUID",
                "NOW" => "Current Time",
                "IF" => "Conditional",
                "DATE_TO_STRING" => "Date to String",
                "JSON_PARSE" => "JSON Parse",
                "DB_FIRST" => "Database Query",
                
                // String Functions
                "STRING_CONCAT" => "String Concatenation",
                "STRING_SUBSTRING" => "String Substring",
                "STRING_REPLACE" => "String Replace",
                "STRING_TRIM" => "String Trim",
                "STRING_UPPER" => "String to Upper",
                "STRING_LOWER" => "String to Lower",
                "STRING_SPLIT" => "String Split",
                "STRING_LENGTH" => "String Length",
                "STRING_CONTAINS" => "String Contains",
                "STRING_STARTS_WITH" => "String Starts With",
                "STRING_ENDS_WITH" => "String Ends With",
                
                // Array Functions
                "ARRAY_LENGTH" => "Array Length",
                "ARRAY_CONTAINS" => "Array Contains",
                "ARRAY_FIRST" => "Array First Element",
                "ARRAY_LAST" => "Array Last Element",
                "ARRAY_JOIN" => "Array Join",
                "GROUP_BY_DYNAMIC_FIELD" => "Dynamic Group By",
                
                // Date Functions
                "DATE_ADD" => "Date Add/Subtract",
                "DATE_DIFF" => "Date Difference",
                "DATE_FORMAT" => "Date Format",
                "DATE_PARSE" => "Date Parse",
                "DATE_TIMESTAMP" => "Date to Timestamp",
                "DATE_FROM_TIMESTAMP" => "Timestamp to Date",
                
                // Dictionary Functions
                "DICT_GET" => "Dictionary Get",
                "DICT_SET" => "Dictionary Set",
                "DICT_MERGE" => "Dictionary Merge",
                "DICT_KEYS" => "Dictionary Keys",
                "DICT_VALUES" => "Dictionary Values",
                "DICT_HAS_KEY" => "Dictionary Has Key",
                
                // Math Functions
                "COMPARE" => "Number Compare",
                "MATH_CALC" => "Math Calculation",
                "MATH_ROUND" => "Math Round",
                "MATH_CEIL" => "Math Ceiling",
                "MATH_FLOOR" => "Math Floor",
                "MATH_ABS" => "Math Absolute",
                
                _ => functionName
            };
        }

        /// <summary>
        /// 获取函数描述
        /// </summary>
        private string GetFunctionDescription(string functionName)
        {
            return functionName switch
            {
                // Common Functions
                "UUID" => "Generate a random UUID string",
                "NOW" => "Get current date and time",
                "IF" => "Return value based on condition",
                "DATE_TO_STRING" => "Convert date to formatted string",
                "JSON_PARSE" => "Parse JSON string to object",
                "DB_FIRST" => "Execute SQL query and return first result",
                
                // String Functions
                "STRING_CONCAT" => "Concatenate multiple values into a string",
                "STRING_SUBSTRING" => "Extract substring from specified position",
                "STRING_REPLACE" => "Replace specified content in string",
                "STRING_TRIM" => "Remove leading and trailing whitespace",
                "STRING_UPPER" => "Convert string to uppercase",
                "STRING_LOWER" => "Convert string to lowercase",
                "STRING_SPLIT" => "Split string by separator",
                "STRING_LENGTH" => "Get string length",
                "STRING_CONTAINS" => "Check if string contains specified value",
                "STRING_STARTS_WITH" => "Check if string starts with specified value",
                "STRING_ENDS_WITH" => "Check if string ends with specified value",
                
                // Array Functions
                "ARRAY_LENGTH" => "Get array or collection length",
                "ARRAY_CONTAINS" => "Check if array contains specified element",
                "ARRAY_FIRST" => "Get first element of array",
                "ARRAY_LAST" => "Get last element of array",
                "ARRAY_JOIN" => "Join array elements into string",
                "GROUP_BY_DYNAMIC_FIELD" => "Group array by dynamic fields",
                
                // Date Functions
                "DATE_ADD" => "Add or subtract time from date",
                "DATE_DIFF" => "Calculate difference between two dates",
                "DATE_FORMAT" => "Format date to string",
                "DATE_PARSE" => "Parse string to date",
                "DATE_TIMESTAMP" => "Convert date to timestamp",
                "DATE_FROM_TIMESTAMP" => "Convert timestamp to date",
                
                // Dictionary Functions
                "DICT_GET" => "Get value from dictionary by key",
                "DICT_SET" => "Set key-value pair in dictionary",
                "DICT_MERGE" => "Merge multiple dictionaries",
                "DICT_KEYS" => "Get all keys from dictionary",
                "DICT_VALUES" => "Get all values from dictionary",
                "DICT_HAS_KEY" => "Check if dictionary contains key",
                
                // Math Functions
                "COMPARE" => "Compare two numeric values",
                "MATH_CALC" => "Perform basic mathematical operations",
                "MATH_ROUND" => "Round number to specified decimal places",
                "MATH_CEIL" => "Round number up to nearest integer",
                "MATH_FLOOR" => "Round number down to nearest integer",
                "MATH_ABS" => "Get absolute value of number",
                
                _ => $"Execute {functionName} function"
            };
        }

        /// <summary>
        /// 获取函数分类
        /// </summary>
        private string GetFunctionCategory(string functionName)
        {
            return functionName switch
            {
                "UUID" or "NOW" or "IF" or "DATE_TO_STRING" or "JSON_PARSE" or "DB_FIRST" => "Common",
                "STRING_CONCAT" or "STRING_SUBSTRING" or "STRING_REPLACE" or "STRING_TRIM" or 
                "STRING_UPPER" or "STRING_LOWER" or "STRING_SPLIT" or "STRING_LENGTH" or 
                "STRING_CONTAINS" or "STRING_STARTS_WITH" or "STRING_ENDS_WITH" => "String",
                "ARRAY_LENGTH" or "ARRAY_CONTAINS" or "ARRAY_FIRST" or "ARRAY_LAST" or 
                "ARRAY_JOIN" or "GROUP_BY_DYNAMIC_FIELD" => "Array",
                "DATE_ADD" or "DATE_DIFF" or "DATE_FORMAT" or "DATE_PARSE" or 
                "DATE_TIMESTAMP" or "DATE_FROM_TIMESTAMP" => "DateTime",
                "DICT_GET" or "DICT_SET" or "DICT_MERGE" or "DICT_KEYS" or 
                "DICT_VALUES" or "DICT_HAS_KEY" => "Dictionary",
                "COMPARE" or "MATH_CALC" or "MATH_ROUND" or "MATH_CEIL" or 
                "MATH_FLOOR" or "MATH_ABS" => "Math",
                _ => "Other"
            };
        }

        /// <summary>
        /// 获取参数类型名称
        /// </summary>
        private string GetParameterTypeName(Type type)
        {
            if (type == typeof(string)) return "string";
            if (type == typeof(int) || type == typeof(int?)) return "number";
            if (type == typeof(double) || type == typeof(double?)) return "number";
            if (type == typeof(bool) || type == typeof(bool?)) return "boolean";
            if (type == typeof(DateTime) || type == typeof(DateTime?)) return "DateTime";
            if (type.IsArray || (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>))) return "array";
            if (type == typeof(object) || type.Name.Contains("Dictionary")) return "object";
            return "any";
        }

        /// <summary>
        /// 获取参数描述
        /// </summary>
        private string GetParameterDescription(string paramName)
        {
            return paramName switch
            {
                "input" => "Input string",
                "array" => "Input array",
                "dict" => "Dictionary object",
                "date" => "Date object",
                "date1" => "First date",
                "date2" => "Second date",
                "value" => "Input value",
                "value1" => "First value",
                "value2" => "Second value",
                "condition" => "Boolean condition",
                "trueValue" => "Value when condition is true",
                "falseValue" => "Value when condition is false",
                "key" => "Dictionary key",
                "time" => "Time object",
                "jsonString" => "JSON string",
                "dateString" => "Date string",
                "timestamp" => "Unix timestamp",
                "separator" => "Separator character",
                "format" => "Format string",
                "startIndex" => "Start position",
                "length" => "Length",
                "oldValue" => "Value to replace",
                "newValue" => "New value",
                "defaultValue" => "Default value",
                "digits" => "Decimal places",
                "unit" => "Time unit",
                "groupFields" => "Group fields array",
                "fields" => "Other fields array",
                "childField" => "Child field name",
                "dict1" => "First dictionary",
                "dict2" => "Second dictionary",
                "operator_" => "Math operator",
                "sqlString" => "SQL query string",
                "args" => "Query parameters",
                _ => $"{paramName} parameter"
            };
        }

        /// <summary>
        /// 生成函数脚本
        /// </summary>
        private string GenerateFunctionScript(string functionName, ParameterInfo[] parameters)
        {
            var paramList = parameters.Select(p =>
            {
                if (p.HasDefaultValue)
                {
                    var defaultValue = p.DefaultValue?.ToString() ?? "null";
                    if (p.ParameterType == typeof(string) && p.DefaultValue != null)
                        defaultValue = $"\"{defaultValue}\"";
                    return p.Name;
                }
                return p.Name;
            });

            return $"Utils.{functionName}({string.Join(", ", paramList)})";
        }

        /// <summary>
        /// 获取输出类型
        /// </summary>
        private string GetOutputType(string functionName)
        {
            return functionName switch
            {
                "UUID" or "STRING_CONCAT" or "STRING_SUBSTRING" or "STRING_REPLACE" or
                "STRING_TRIM" or "STRING_UPPER" or "STRING_LOWER" or "DATE_TO_STRING" or
                "DATE_FORMAT" or "ARRAY_JOIN" => "string",

                "ARRAY_LENGTH" or "STRING_LENGTH" or "DATE_DIFF" or "DATE_TIMESTAMP" or
                "MATH_CALC" or "MATH_ROUND" or "MATH_CEIL" or "MATH_FLOOR" or "MATH_ABS" => "number",

                "IF" or "COMPARE" or "ARRAY_CONTAINS" or "STRING_CONTAINS" or
                "STRING_STARTS_WITH" or "STRING_ENDS_WITH" or "DICT_HAS_KEY" => "boolean",

                "NOW" or "DATE_ADD" or "DATE_PARSE" or "DATE_FROM_TIMESTAMP" => "DateTime",

                "STRING_SPLIT" or "DICT_KEYS" or "DICT_VALUES" or "GROUP_BY_DYNAMIC_FIELD" => "array",

                _ => "object"
            };
        }

        #endregion
    }
}
